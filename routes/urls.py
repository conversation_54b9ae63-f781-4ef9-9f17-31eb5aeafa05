from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
from database import urls_collection
from fastapi.responses import RedirectResponse


router = APIRouter()

class URLMap(BaseModel):
    long_url : str
    short_code : Optional[str] = None

@router.post('/shorten')
async def shorten_url(data:URLMap):
    doc = data.model_dump()
    doc["short_code"] = doc["short_code"] or "dummy123"   

    verify_code = await urls_collection.find_one({"short_code": doc["short_code"]})
    if verify_code:
        raise HTTPException(status_code=400, detail="This short code already exits")
    await urls_collection.insert_one(doc)
    return {
        "short_url":f'http://127.0.0.1:8000/{doc["short_code"]}',
        "long_url": doc["long_url"],
        "short_code": doc["short_code"] 
    }

@router.get('/{short_code}')
async def redirect_link(short_code:str):
    doc = await urls_collection.find_one({"short_code" : short_code})
    if not doc:
        raise HTTPException(status_code=404, detail="Short link not found")
    return RedirectResponse(url=doc["long_url"], status_code=307)