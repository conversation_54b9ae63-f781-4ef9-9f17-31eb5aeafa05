* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background: linear-gradient(-45deg, #F6F1DE, #3E3F5B, #F6F1DE, #3E3F5B);
  color: #111;
  line-height: 1.6;
  animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.header {
  padding: 10px;
  font-weight: bold;
}

.logo {
  font-weight: bold;
  font-family: "Roboto Condensed", sans-serif;
}

#hero {
  /* position: relative; */
  justify-content: space-between;
  align-items: center;
  padding: 60px;
  background: white;
  margin-top: 0;
}

.hero {
  width: 100%;
  position: absolute;
  margin-top: 600px;
  color: #434566;
}

.learn {
  padding: 12px 24px;
  background-color: #111;
  color: #fff;
  border-radius: 50px;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9rem;
  display: inline-block;
}

.hero-right {
  max-width: 100%;
  position: absolute;
  justify-content: center;
  margin-left: 350px;
  margin-top: 417px;
  height: 500px;
  z-index: -1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile {
  width: 600px;
  object-fit: cover;
  border-bottom-left-radius: 40%;
  border-bottom-right-radius: 40%;
  height: 650px;
  margin-left: 95.5px;
  /* border: 5px solid #f4ebeb; */
  opacity: 0.9;
}

.name-below {
  text-align: center;
  font-size: 180px;
  font-weight: 700;
  position: absolute;
  z-index: -1;
  color: #2d2d2d;
  margin-left: 125px;
  font-family: 'Georgia', serif;
  margin-top: 410px;
}

/* about */
#about {
  padding: 100px;
  /* background-color: #F6F1DE; */
  color: #111;
  font-family: 'Inter', sans-serif;
}

.about-container {
  width: 800px;
  height: 500px;
  margin: 0 auto;
  text-align: center;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 2px 4px 8px #2a2b41;
  border: 1px solid black;
}

.about-container:hover {
  box-shadow: 4px 8px 16px black;
  transition: box-shadow 0.3s ease;
}

.section-title {
  font-size: 48px;
  font-family: 'Playfair Display', serif;
  margin-bottom: 30px;
  color: #3E3F5B;
  margin-top: 10px;
}

.about-text {
  font-size: 18px;
  line-height: 1.8;
  margin-bottom: 20px;
  color: #3E3F5B;
}

/* project */
.projecttitle {
  text-align: center;
  font-size: 50px;
  font-family: 'Playfair Display', serif;
  margin-bottom: 30px;
  color: #3E3F5B;
  margin-top: 10px;
}

.my-project {
  display: flex;
  flex-direction: column;
  gap: 10%;
  align-items: center;
  position: relative;
  width: 80%;
  height: 100vh;
  margin-top: 200px;
  margin-bottom: 700px;
}

.project-card {
  display: flex;
  width: 100%;
  height: 40%;
  align-items: center;
  gap: 10%;
  justify-content: center;
}

.project-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  /* mix-blend-mode:exclusion; */
  position: relative;
  cursor: pointer;
  transition: 0.5s;
  width: 700px;
  margin: 10px;
  margin-left: 100px;
}

.project-img img {
  object-fit: cover;
  width: 100%;
  box-shadow: 0 0 10px lightgray;
  border-radius: 20px;
  transition: 0.5s;
}

.project-card img:hover {
  box-shadow: 2px 4px 25px rgb(1, 1, 1);
}

.project-info {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  width: 70%;
  padding-left: 10%;
  margin-right: 100px;
}

.project-info h1 {
  width: 90%;
  font-size: 25px;
  font-weight: bolder;
  text-wrap: nowrap;
  margin-top: 0;
  margin-bottom: 10px;
  width: 450px;
}

.project-info p {
  width: 90%;
  max-width: 400px;
  min-width: 300px;
  margin-bottom: 50px;
  margin-top: 0;
}

.project-info button {
  color: white;
  padding: 15px 35px;
  border-radius: 10px;
  border: 1px solid #72a1de81;
  background-color: #2200493d;
  box-shadow: 0 0 5px #72a1de81;
  cursor: pointer;
  transition: 0.3s;
}

.project-info button:hover {
  opacity: 0.8;
  box-shadow: 0 0 15px #72a1de81;
}

.project-img .hover-sign {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30%;
  height: 100px;
}

.hover-sign::before,
.hover-sign::after {
  /* content: "👆"; */
  text-align: center;
  position: absolute;
  font-size: 50px;
  top: 20%;
  left: 40%;
  border-radius: 40px;
  animation: hover-animation 4s ease-in-out infinite;
}

.hover-sign.active {
  display: none;
}




/* Skills section */
.skill-title {
  text-align: center;
  font-size: 50px;
  font-family: 'Playfair Display', serif;
  margin-bottom: 30px;
  color: #cccde4;
  margin-top: 10px;
}

.skill-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 600px;
  margin: 0px auto;
  border-radius: 60%;
  /* background: #F6F1DE; */
  padding: 90px;
  /* padding: 40px;
  box-shadow: 2px 4px 8px black;
  border: 1px solid #F6F1DE; */
}

.center-image {
  width: 300px;
  height: 300px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  object-fit: cover;
}

.item {
  position: absolute;
  text-align: center;
  width: 100px;
}

.item img {
  width: 85px;
  height: 70px;
}

.item img:hover {
  transform: rotate(5deg) scale(1.1);
}

.item p {
  font-size: 12px;
  margin-top: 5px;
}

#skill {
  /* text-align: center; */
  font-size: 180px;
  font-weight: 700;
  position: relative;
  z-index: -1;
  color: #2d2d2d;
  margin-right: 50px;
  font-family: 'Georgia', serif;
  /* margin-top: 40px; */
  opacity: 0.19;
  margin-left: 1px;
  padding: 1px;
}

/* experience */
.exp-title {
  text-align: center;
  font-size: 50px;
  font-family: 'Playfair Display', serif;
  margin-bottom: 30px;
  color: #F6F1DE;
  margin-top: 20px;
}

.timeline {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1000px;
  margin: auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 40px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #F6F1DE;
  z-index: 1;
}

.titem {
  text-align: center;
  width: 22%;
  position: relative;
  z-index: 2;
}

.titem::before {
  content: '';
  width: 20px;
  height: 20px;
  background-color: #3E3F5B;
  border-radius: 50%;
  border: 3px solid white;
  display: block;
  margin: auto;
  margin-bottom: 10px;
  box-shadow: 0 0 0 3px #3E3F5B;
  margin-top: 30px;
  cursor: pointer;
}

.titem:hover::before {
  background-color: #2e7d32;
  transform: scale(1.3);
  box-shadow: 0 0 10px #2e7d32;
  transition: all 0.3s ease;
}

.title {
  font-weight: bolder;
  color: #F6F1DE;
  font-size: 16px;
}

.date {
  font-size: 14px;
  color: #888;
  margin: 5px 0;
}

.description {
  font-size: 14px;
  color: #F6F1DE;
}

/* contact */
.lets-talk {
  /* background: #f7f7f9; */
  padding: 60px 0;
  text-align: center;
}

.lets-talk-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 0 20px;
}

.lets-talk h1 {
  text-align: center;
  font-size: 50px;
  font-family: 'Playfair Display', serif;
  margin-bottom: 30px;
  color: #F6F1DE;
  margin-top: 20px;
}

.lets-talk p {
  font-size: 1.1rem;
  color: #F6F1DE;
}

.lets-talk a {
  color: #3578e5;
  text-decoration: underline;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-form input,
.contact-form textarea {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  resize: none;
}

.contact-form button {
  color: white;
  padding: 15px 35px;
  border-radius: 10px;
  border: 1px solid #72a1de81;
  background-color: #2200493d;
  box-shadow: 0 0 5px #72a1de81;
  cursor: pointer;
  transition: 0.3s;
}

.contact-form button:hover {
  opacity: 0.8;
  box-shadow: 0 0 15px #72a1de81;
}

/* footer */
.footer {
  font-size: 10px;
  background-color: #3E3F5B;
  height: 50px;
}

.footer h1 {
  color: #F6F1DE;
  text-align: center;
  padding: 10px;
}

#hero {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 5px;
  justify-content: space-between;
  margin: auto;
  background: transparent;
}

.nav ul li {
  list-style: none;
  display: inline-block;
  padding: 5px;
  margin: 10px;
  margin-right: 40px;
}

.nav ul li a {
  text-decoration: none;
  font-weight: bold;
  color: rgb(61, 19, 101);
}

/* Media Queries */
/* @media screen and (max-width: 768px) {
  .hero {
    flex-direction: column;
    padding: 40px 30px;
    background: #f3f0ec;
  }
  .hero-left, .hero-right {
    max-width: 100%;
    text-align: center;
  }
  .hero-right {
    margin-top: 30px;
  }
  .services {
    flex-direction: column;
    align-items: center;
  }
} */